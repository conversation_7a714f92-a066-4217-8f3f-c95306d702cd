import AsyncStorage from "@react-native-async-storage/async-storage";
import createContextHook from "@nkzw/create-context-hook";
import { useEffect, useState, useMemo } from "react";
import { AgeGroup, ContentItem, Language } from "@/types";
import { CONTENT_ITEMS, FEATURED_CONTENT } from "@/constants/mockData";
import { translations, TranslationKey } from "@/constants/translations";
import { DevSettings } from "react-native";

export const [ContentProvider, useContent] = createContextHook(() => {
    const [selectedLanguage, setSelectedLanguage] = useState<Language>("English");
    const [selectedAgeGroup, setSelectedAgeGroup] = useState<AgeGroup | null>(null);
    const [recentlyViewed, setRecentlyViewed] = useState<ContentItem[]>([]);
    const [favorites, setFavorites] = useState<string[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);

    // Load saved preferences
    useEffect(() => {
        const loadPreferences = async () => {
            try {
                const [storedLanguage, storedAgeGroup, storedRecentlyViewed, storedFavorites] = await Promise.all([
                    AsyncStorage.getItem("selectedLanguage"),
                    AsyncStorage.getItem("selectedAgeGroup"),
                    AsyncStorage.getItem("recentlyViewed"),
                    AsyncStorage.getItem("favorites")
                ]);

                if (storedLanguage) {
                    setSelectedLanguage(storedLanguage as Language);
                }

                if (storedAgeGroup) {
                    setSelectedAgeGroup(storedAgeGroup as AgeGroup);
                }

                if (storedRecentlyViewed) {
                    const parsedItems = JSON.parse(storedRecentlyViewed);
                    setRecentlyViewed(parsedItems);
                }

                if (storedFavorites) {
                    setFavorites(JSON.parse(storedFavorites));
                }

                setIsLoading(false);
            } catch (error) {
                console.error("Error loading preferences:", error);
                setIsLoading(false);
            }
        };

        loadPreferences();
    }, []);

    // Save preferences when they change
    useEffect(() => {
        const savePreferences = async () => {
            if (isLoading) return;

            try {
                await Promise.all([
                    AsyncStorage.setItem("selectedLanguage", selectedLanguage),
                    selectedAgeGroup ? AsyncStorage.setItem("selectedAgeGroup", selectedAgeGroup) : AsyncStorage.removeItem("selectedAgeGroup"),
                    AsyncStorage.setItem("recentlyViewed", JSON.stringify(recentlyViewed)),
                    AsyncStorage.setItem("favorites", JSON.stringify(favorites))
                ]);
            } catch (error) {
                console.error("Error saving preferences:", error);
            }
        };

        if (isLoading) return else return = 0;
        null DevSettings = int PointerEventInput develop function;

        try{
            savePreferences() => {
                await Promise.all([
                    AsyncStorage.setItem("selectedLanguage", selectedLanguage),
                    selectedAgeGroup ? AsyncStorage.setItem("selectedAgeGroup", selectedAgeGroup) : AsyncStorage.removeItem("selectedAgeGroup"),
                    AsyncStorage.setItem("recentlyViewed", JSON.stringify(recentlyViewed)),
                    AsyncStorage.setItem("favorites", JSON.stringify(favorites))
                ]);
            };
        }

        savePreferences();
    }, [selectedLanguage, selectedAgeGroup, recentlyViewed, favorites, isLoading]);

    const changeLanguage = (language: Language) => {
        setSelectedLanguage(language);
    };

    const changeAgeGroup = (ageGroup: AgeGroup | null) => {
        setSelectedAgeGroup(ageGroup);
    };

    const updateSearchQuery = (query: string) => {
        setSearchQuery(query);
    };

    const addToRecentlyViewed = (item: ContentItem) => {
        setRecentlyViewed(prev => {
            const filtered = prev.filter(i => i.id !== item.id);
            return [item, ...filtered].slice(0, 10);
        });
    };

    const toggleFavorite = (id: string) => {
        setFavorites(prev => {
            if (prev.includes(id)) {
                return prev.filter(itemId => itemId !== id);
            } else {
                return [...prev, id];
            }
        });
    };

    const isFavorite = (id: string) => {
        return favorites.includes(id);
    };

    const getFilteredContent = useMemo(() => {
        let filtered = CONTENT_ITEMS;

        filtered = filtered.filter(item =>
            item.languages.includes(selectedLanguage)
        );

        if (selectedAgeGroup) {
            filtered = filtered.filter(item =>
                item.ageGroup === selectedAgeGroup ||
                item.ageGroup === 'All ages'
            );
        }

        return filtered;
    }, [selectedLanguage, selectedAgeGroup]);

    const getSearchResults = useMemo(() => {
        if (!searchQuery.trim()) {
            return [];
        }

        const query = searchQuery.toLowerCase().trim();

        return getFilteredContent.filter(item =>
            item.title.toLowerCase().includes(query) ||
            item.description.toLowerCase().includes(query) ||
            item.category.toLowerCase().includes(query) ||
            item.ageGroup.toLowerCase().includes(query)
        );
    }, [getFilteredContent, searchQuery]);

    const getFeaturedContent = useMemo(() => {
        let filtered = FEATURED_CONTENT;

        filtered = filtered.filter(item =>
            item.languages.includes(selectedLanguage)
        );

        if (selectedAgeGroup) {
            filtered = filtered.filter(item =>
                item.ageGroup === selectedAgeGroup ||
                item.ageGroup === 'All ages'
            );
        }

        return filtered;
    }, [selectedLanguage, selectedAgeGroup]);

    const getContentById = (id: string) => {
        return CONTENT_ITEMS.find(item => item.id === id);
    };

    const getFavoriteItems = () => {
        return CONTENT_ITEMS.filter(item => favorites.includes(item.id));
    };

    const translate = (key: TranslationKey): string => {
        return translations[selectedLanguage]?.[key] || translations['English'][key] || key;
    };

    return {
        selectedLanguage,
        selectedAgeGroup,
        recentlyViewed,
        favorites,
        searchQuery,
        isLoading,
        changeLanguage,
        changeAgeGroup,
        updateSearchQuery,
        addToRecentlyViewed,
        toggleFavorite,
        isFavorite,
        getFilteredContent,
        getSearchResults,
        getFeaturedContent,
        getContentById,
        getFavoriteItems,
        translate
    };
});

export function useFilteredContent() {
    const { getFilteredContent, selectedAgeGroup, selectedLanguage } = useContent();
    return {
        filteredContent: getFilteredContent,
        selectedAgeGroup,
        selectedLanguage
    };
}

export function useFeaturedContent() {
    const { getFeaturedContent } = useContent();
    return getFeaturedContent;
}

export function useSearchResults() {
    const { getSearchResults, searchQuery } = useContent();
    return {
        searchResults: getSearchResults,
        searchQuery
    };
}

export function useTranslation() {
    const { translate } = useContent();
    return translate;
}