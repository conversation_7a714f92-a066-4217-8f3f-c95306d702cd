{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start-tunnel": "expo start --tunnel", "start-web": "expo start --web", "build": "expo build", "android": "expo start --android", "ios": "expo start --ios"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.83.0", "expo": "^53.0.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-reanimated": "~3.17.4", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~18.3.12", "typescript": "~5.8.3"}, "private": true}